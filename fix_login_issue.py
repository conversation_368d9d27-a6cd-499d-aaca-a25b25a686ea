#!/usr/bin/env python3
"""
修复登录问题脚本
1. 确保数据库中有正确的演示用户
2. 验证后端API是否正常工作
3. 提供正确的登录凭据
"""

import sqlite3
import hashlib
import bcrypt
import requests
import json
import os
from pathlib import Path

def hash_password_bcrypt(password: str) -> str:
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def verify_password_bcrypt(password: str, hashed: str) -> bool:
    """验证bcrypt密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def create_demo_users():
    """创建演示用户"""
    print("🔧 创建演示用户...")
    
    # 数据库路径
    db_path = "backend/quant_platform.db"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建用户表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            hashed_password VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            is_superuser BOOLEAN DEFAULT 0,
            is_verified BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 演示用户数据
    demo_users = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'full_name': '系统管理员',
            'password': 'admin123',  # 使用admin123作为密码
            'is_superuser': 1
        },
        {
            'username': 'demo',
            'email': '<EMAIL>', 
            'full_name': '演示用户',
            'password': 'demo123',
            'is_superuser': 0
        }
    ]
    
    for user_data in demo_users:
        # 检查用户是否存在
        cursor.execute('SELECT id, hashed_password FROM users WHERE username = ?', (user_data['username'],))
        existing_user = cursor.fetchone()
        
        # 哈希密码
        hashed_password = hash_password_bcrypt(user_data['password'])
        
        if existing_user:
            # 更新现有用户
            cursor.execute('''
                UPDATE users 
                SET hashed_password = ?, email = ?, full_name = ?, is_active = 1, 
                    is_superuser = ?, is_verified = 1, updated_at = CURRENT_TIMESTAMP
                WHERE username = ?
            ''', (
                hashed_password,
                user_data['email'],
                user_data['full_name'],
                user_data['is_superuser'],
                user_data['username']
            ))
            print(f"✅ 更新用户: {user_data['username']} / {user_data['password']}")
        else:
            # 创建新用户
            cursor.execute('''
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
                VALUES (?, ?, ?, ?, 1, ?, 1)
            ''', (
                user_data['username'],
                user_data['email'],
                user_data['full_name'],
                hashed_password,
                user_data['is_superuser']
            ))
            print(f"✅ 创建用户: {user_data['username']} / {user_data['password']}")
    
    conn.commit()
    
    # 验证用户创建
    print("\n📋 数据库用户列表:")
    cursor.execute('SELECT username, email, is_active, is_superuser FROM users')
    users = cursor.fetchall()
    for user in users:
        username, email, is_active, is_superuser = user
        status = "✅ 活跃" if is_active else "❌ 禁用"
        role = "管理员" if is_superuser else "普通用户"
        print(f"  - {username} ({email}) - {status} - {role}")
    
    conn.close()
    return True

def test_backend_connection():
    """测试后端连接"""
    print("\n🌐 测试后端连接...")
    
    backend_urls = [
        'http://localhost:8000',
        'http://localhost:8001',
        'http://127.0.0.1:8000',
        'http://127.0.0.1:8001'
    ]
    
    for url in backend_urls:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ 后端服务运行在: {url}")
                return url
        except requests.exceptions.RequestException:
            pass
    
    print("❌ 后端服务未运行")
    print("请先启动后端服务:")
    print("  - backend\\start_windows.bat")
    print("  - 或 python backend\\start_backend.py")
    return None

def test_login_api(base_url):
    """测试登录API"""
    print(f"\n🧪 测试登录API ({base_url})...")
    
    test_credentials = [
        {'username': 'admin', 'password': 'admin123'},
        {'username': 'demo', 'password': 'demo123'}
    ]
    
    for creds in test_credentials:
        try:
            response = requests.post(
                f"{base_url}/api/v1/auth/login",
                json=creds,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {creds['username']}/{creds['password']} - 登录成功")
                print(f"   Token: {data.get('access_token', 'N/A')[:50]}...")
            else:
                print(f"❌ {creds['username']}/{creds['password']} - 登录失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {creds['username']}/{creds['password']} - 连接失败: {e}")

def check_frontend_config():
    """检查前端配置"""
    print("\n⚙️ 检查前端配置...")
    
    config_file = "frontend/src/config/index.ts"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'localhost:8000' in content:
            print("✅ 前端配置指向 localhost:8000")
        elif 'localhost:8001' in content:
            print("⚠️ 前端配置指向 localhost:8001")
        else:
            print("❓ 未找到明确的后端URL配置")
    else:
        print("❌ 前端配置文件不存在")

def create_login_test_page():
    """创建登录测试页面"""
    print("\n📄 创建登录测试页面...")
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .demo-accounts { background: #e2e3e5; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>量化投资平台 - 登录测试</h1>
    
    <div class="demo-accounts">
        <h3>演示账户</h3>
        <p><strong>管理员:</strong> admin / admin123</p>
        <p><strong>演示用户:</strong> demo / demo123</p>
    </div>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" value="admin123" required>
        </div>
        
        <button type="submit">测试登录</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>登录成功!</h4>
                            <p><strong>Token:</strong> ${data.access_token.substring(0, 50)}...</p>
                            <p><strong>用户:</strong> ${data.user.username} (${data.user.full_name})</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>登录失败</h4>
                            <p>${data.detail || '未知错误'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>连接失败</h4>
                        <p>无法连接到后端服务: ${error.message}</p>
                        <p>请确保后端服务正在运行在 http://localhost:8000</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>'''
    
    with open('login_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 创建了 login_test.html")
    print("   在浏览器中打开此文件来测试登录功能")

def main():
    """主函数"""
    print("🔧 修复登录问题...")
    print("=" * 50)
    
    # 1. 创建演示用户
    create_demo_users()
    
    # 2. 检查前端配置
    check_frontend_config()
    
    # 3. 测试后端连接
    backend_url = test_backend_connection()
    
    # 4. 如果后端运行，测试登录API
    if backend_url:
        test_login_api(backend_url)
    
    # 5. 创建测试页面
    create_login_test_page()
    
    print("\n" + "=" * 50)
    print("✅ 修复完成!")
    print("\n📱 演示登录账户:")
    print("   - admin / admin123 (管理员)")
    print("   - demo / demo123 (演示用户)")
    print("\n🌐 登录地址:")
    print("   - http://localhost:5173/login")
    print("\n🧪 测试登录:")
    print("   - 打开 login_test.html 进行API测试")
    print("\n🚀 启动服务:")
    print("   - 后端: backend\\start_windows.bat")
    print("   - 前端: cd frontend && pnpm dev")

if __name__ == "__main__":
    main()
