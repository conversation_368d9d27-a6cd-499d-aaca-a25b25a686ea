# 本地开发环境启动指南

## 概述

本目录包含量化投资平台本地开发环境的 Docker Compose 配置。

## 快速启动

### 1. 环境准备

```bash
# 复制环境变量文件
cp .env.local .env

# 编辑环境变量（如需要）
nano .env
```

### 2. 启动服务

```bash
# 在项目根目录执行
docker compose -f docker/compose/local/docker-compose.yml up -d

# 或者使用 make 命令（如果有 Makefile）
make dev-up
```

### 3. 验证服务

```bash
# 检查服务状态
docker compose -f docker/compose/local/docker-compose.yml ps

# 查看日志
docker compose -f docker/compose/local/docker-compose.yml logs -f
```

## 服务访问

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端 | http://localhost:5173 | Vue3 开发服务器 |
| 后端 API | http://localhost:8000 | FastAPI 服务器 |
| API 文档 | http://localhost:8000/docs | Swagger UI |
| 数据库 | localhost:5432 | PostgreSQL |
| Redis | localhost:6379 | Redis 缓存 |

## 开发工作流

### 代码热重载

- **前端**：修改 `frontend/` 下的文件会自动重载
- **后端**：修改 `backend/` 下的文件会自动重载

### 数据库操作

```bash
# 连接数据库
docker compose -f docker/compose/local/docker-compose.yml exec postgres psql -U postgres -d quantplatform

# 运行数据库迁移
docker compose -f docker/compose/local/docker-compose.yml exec backend alembic upgrade head

# 重置数据库
docker compose -f docker/compose/local/docker-compose.yml exec backend alembic downgrade base
docker compose -f docker/compose/local/docker-compose.yml exec backend alembic upgrade head
```

### 查看日志

```bash
# 查看所有服务日志
docker compose -f docker/compose/local/docker-compose.yml logs -f

# 查看特定服务日志
docker compose -f docker/compose/local/docker-compose.yml logs -f backend
docker compose -f docker/compose/local/docker-compose.yml logs -f frontend
docker compose -f docker/compose/local/docker-compose.yml logs -f celery-worker
```

### 进入容器

```bash
# 进入后端容器
docker compose -f docker/compose/local/docker-compose.yml exec backend bash

# 进入前端容器
docker compose -f docker/compose/local/docker-compose.yml exec frontend sh

# 进入数据库容器
docker compose -f docker/compose/local/docker-compose.yml exec postgres psql -U postgres -d quantplatform
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5173
   netstat -tulpn | grep :8000
   
   # 修改 .env 文件中的端口配置
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   docker compose -f docker/compose/local/docker-compose.yml ps postgres
   
   # 查看数据库日志
   docker compose -f docker/compose/local/docker-compose.yml logs postgres
   ```

3. **前端构建失败**
   ```bash
   # 清理 node_modules 并重新安装
   docker compose -f docker/compose/local/docker-compose.yml exec frontend rm -rf node_modules
   docker compose -f docker/compose/local/docker-compose.yml exec frontend pnpm install
   ```

4. **后端依赖问题**
   ```bash
   # 重新安装 Python 依赖
   docker compose -f docker/compose/local/docker-compose.yml exec backend pip install -r requirements.txt
   ```

### 重置环境

```bash
# 停止并删除所有容器、网络、卷
docker compose -f docker/compose/local/docker-compose.yml down -v

# 清理镜像（可选）
docker compose -f docker/compose/local/docker-compose.yml down --rmi all

# 重新构建并启动
docker compose -f docker/compose/local/docker-compose.yml up --build -d
```

## 性能优化

### 开发环境优化建议

1. **增加内存限制**（如果需要）：
   ```yaml
   # 在 docker-compose.yml 中添加
   deploy:
     resources:
       limits:
         memory: 2G
   ```

2. **使用 Docker 卷缓存**：
   - node_modules 已配置为匿名卷
   - Python 包缓存已优化

3. **网络优化**：
   - 使用自定义网络减少延迟
   - 服务间通信使用容器名

## 环境变量说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `POSTGRES_PASSWORD` | password | 数据库密码 |
| `SECRET_KEY` | dev-secret-key-2024 | 应用密钥 |
| `LOG_LEVEL` | DEBUG | 日志级别 |
| `CORS_ORIGINS` | http://localhost:5173 | CORS 允许的源 |

## 下一步

- 配置 IDE 连接到容器内的服务
- 设置代码格式化和 linting
- 配置测试环境
- 查看生产环境配置：`../production/README.md`
