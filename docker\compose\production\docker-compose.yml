# 生产环境 Docker Compose 配置
# 使用方式: docker compose -f docker/compose/production/docker-compose.yml --env-file .env.prod up -d
version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: quant-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../../nginx/production/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ../../../ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端生产服务
  frontend:
    build:
      context: ../../..
      dockerfile: frontend/Dockerfile.prod
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-/api/v1}
        VITE_WS_URL: ${VITE_WS_URL:-/ws}
        VITE_APP_TITLE: ${VITE_APP_TITLE:-量化投资平台}
        VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0}
    container_name: quant-frontend-prod
    restart: unless-stopped
    expose:
      - "80"
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 后端生产服务
  backend:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-backend-prod
    restart: unless-stopped
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - LOG_LEVEL=${LOG_LEVEL:-WARNING}
      - CORS_ORIGINS=${CORS_ORIGINS}
    volumes:
      - ../../../data:/app/data:ro
      - ../../../logs:/app/logs
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant-postgres-prod
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../../backups:/backups
      - postgres_logs:/var/log/postgresql
    networks:
      - quant-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: quant-redis-prod
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000

  # Celery Worker
  celery-worker:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-celery-worker-prod
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ../../../data:/app/data:ro
      - ../../../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      celery -A app.tasks.celery_app worker
      --loglevel=warning
      --concurrency=4
      --max-tasks-per-child=1000
      --time-limit=3600
      --soft-time-limit=3300

  # Celery Beat
  celery-beat:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-celery-beat-prod
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ../../../data:/app/data:ro
      - ../../../logs:/app/logs
      - celery_beat_data:/app/celerybeat-schedule
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      celery -A app.tasks.celery_app beat
      --loglevel=warning
      --schedule=/app/celerybeat-schedule/celerybeat-schedule

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  postgres_logs:
    driver: local
  redis_logs:
    driver: local
  backend_uploads:
    driver: local
  celery_beat_data:
    driver: local

networks:
  quant-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
