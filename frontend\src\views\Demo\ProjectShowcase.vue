<template>
  <div class="project-showcase">
    <div class="showcase-header">
      <h1 class="main-title">量化投资平台 - 项目展示</h1>
      <p class="subtitle">专业级量化交易系统，完整的前后端架构</p>
      <div class="project-stats">
        <div class="stat-item">
          <div class="stat-number">78%</div>
          <div class="stat-label">项目完成度</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">32</div>
          <div class="stat-label">数据库表</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">12</div>
          <div class="stat-label">API模块</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">50+</div>
          <div class="stat-label">Vue组件</div>
        </div>
      </div>
    </div>

    <div class="showcase-content">
      <!-- 已完成功能展示 -->
      <section class="feature-section">
        <h2 class="section-title">✅ 已完成核心功能</h2>
        
        <!-- 后端架构 -->
        <div class="feature-category">
          <h3 class="category-title">🏗️ 后端架构</h3>
          <div class="feature-grid">
            <div class="feature-card completed">
              <div class="feature-icon">🚀</div>
              <h4>FastAPI服务</h4>
              <p>高性能异步API服务，支持自动文档生成</p>
              <div class="feature-status">已部署运行</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🗄️</div>
              <h4>数据库设计</h4>
              <p>32张表的完整数据模型，支持用户、交易、策略、风控</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🔐</div>
              <h4>JWT认证</h4>
              <p>安全的用户认证与权限管理系统</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">📊</div>
              <h4>行情数据</h4>
              <p>支持30+期货品种的实时行情数据处理</p>
              <div class="feature-status">已完成</div>
            </div>
          </div>
        </div>

        <!-- 前端架构 -->
        <div class="feature-category">
          <h3 class="category-title">🎨 前端架构</h3>
          <div class="feature-grid">
            <div class="feature-card completed">
              <div class="feature-icon">⚡</div>
              <h4>Vue 3 + TypeScript</h4>
              <p>现代化前端框架，类型安全的开发体验</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🎯</div>
              <h4>Element Plus UI</h4>
              <p>专业的企业级UI组件库，美观易用</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🔄</div>
              <h4>WebSocket实时通信</h4>
              <p>实时行情数据推送，交易状态更新</p>
              <div class="feature-status">已优化</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">📱</div>
              <h4>响应式设计</h4>
              <p>支持多设备访问，移动端友好</p>
              <div class="feature-status">已完成</div>
            </div>
          </div>
        </div>

        <!-- 交易功能 -->
        <div class="feature-category">
          <h3 class="category-title">💹 交易功能</h3>
          <div class="feature-grid">
            <div class="feature-card completed">
              <div class="feature-icon">📋</div>
              <h4>订单管理</h4>
              <p>支持限价单、市价单、止损止盈等多种订单类型</p>
              <div class="feature-status">已增强</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">💰</div>
              <h4>持仓管理</h4>
              <p>实时持仓查询，盈亏统计，风险监控</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🎯</div>
              <h4>CTP接口</h4>
              <p>期货交易接口集成，支持模拟交易</p>
              <div class="feature-status">已实现</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">⚡</div>
              <h4>快速下单</h4>
              <p>一键买卖，快速价格设置，智能数量计算</p>
              <div class="feature-status">新增功能</div>
            </div>
          </div>
        </div>

        <!-- 策略与回测 -->
        <div class="feature-category">
          <h3 class="category-title">🤖 策略与回测</h3>
          <div class="feature-grid">
            <div class="feature-card completed">
              <div class="feature-icon">📈</div>
              <h4>策略开发</h4>
              <p>支持Python策略编写，内置常用技术指标</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">🔬</div>
              <h4>回测引擎</h4>
              <p>高性能回测系统，详细的绩效分析</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">📊</div>
              <h4>可视化图表</h4>
              <p>ECharts图表库，K线图、收益曲线等</p>
              <div class="feature-status">已完成</div>
            </div>
            <div class="feature-card completed">
              <div class="feature-icon">⚠️</div>
              <h4>风险管理</h4>
              <p>实时风险监控，止损止盈，仓位控制</p>
              <div class="feature-status">已完成</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 技术亮点 -->
      <section class="tech-highlights">
        <h2 class="section-title">🌟 技术亮点</h2>
        <div class="highlight-grid">
          <div class="highlight-card">
            <div class="highlight-icon">🔧</div>
            <h4>代码质量优化</h4>
            <p>使用Black格式化43个文件，Flake8代码规范检查，提升代码质量</p>
          </div>
          <div class="highlight-card">
            <div class="highlight-icon">📧</div>
            <h4>SMTP邮件服务</h4>
            <p>完善的邮件配置，支持用户注册验证、交易通知等功能</p>
          </div>
          <div class="highlight-card">
            <div class="highlight-icon">🔒</div>
            <h4>安全加固</h4>
            <p>多层安全中间件，CORS配置，IP白名单，速率限制</p>
          </div>
          <div class="highlight-card">
            <div class="highlight-icon">📊</div>
            <h4>监控系统</h4>
            <p>Prometheus指标收集，健康检查，性能监控</p>
          </div>
          <div class="highlight-card">
            <div class="highlight-icon">🚀</div>
            <h4>异步架构</h4>
            <p>全异步处理，WebSocket实时通信，高并发支持</p>
          </div>
          <div class="highlight-card">
            <div class="highlight-icon">🎨</div>
            <h4>现代化UI</h4>
            <p>响应式设计，暗色主题，组件化开发，用户体验优秀</p>
          </div>
        </div>
      </section>

      <!-- 项目修复记录 -->
      <section class="fix-records">
        <h2 class="section-title">🔧 项目修复记录</h2>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker fix-1">1</div>
            <div class="timeline-content">
              <h4>后端服务启动修复</h4>
              <p>修复Python环境配置，成功启动FastAPI服务器，端口8000可访问</p>
              <div class="fix-status completed">✅ 已完成</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker fix-2">2</div>
            <div class="timeline-content">
              <h4>代码质量修复</h4>
              <p>使用Black格式化43个文件，配置Flake8规则，达到代码规范标准</p>
              <div class="fix-status completed">✅ 已完成</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker fix-3">3</div>
            <div class="timeline-content">
              <h4>SMTP配置修复</h4>
              <p>修复邮件服务配置命名问题，添加默认SMTP设置</p>
              <div class="fix-status completed">✅ 已完成</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker fix-4">4</div>
            <div class="timeline-content">
              <h4>WebSocket实时通信优化</h4>
              <p>增强WebSocket服务，支持实时行情数据推送和交易状态更新</p>
              <div class="fix-status completed">✅ 已完成</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker fix-5">5</div>
            <div class="timeline-content">
              <h4>交易界面增强</h4>
              <p>优化交易终端组件，增加快速下单、风险控制、实时数据显示</p>
              <div class="fix-status completed">✅ 已完成</div>
            </div>
          </div>
        </div>
      </section>

      <!-- API文档展示 -->
      <section class="api-showcase">
        <h2 class="section-title">📚 API文档</h2>
        <div class="api-grid">
          <div class="api-module">
            <h4>🔐 认证模块</h4>
            <ul>
              <li>用户注册/登录</li>
              <li>JWT令牌管理</li>
              <li>权限验证</li>
            </ul>
          </div>
          <div class="api-module">
            <h4>📊 行情数据</h4>
            <ul>
              <li>实时行情订阅</li>
              <li>历史数据查询</li>
              <li>技术指标计算</li>
            </ul>
          </div>
          <div class="api-module">
            <h4>💹 交易接口</h4>
            <ul>
              <li>订单管理</li>
              <li>持仓查询</li>
              <li>账户信息</li>
            </ul>
          </div>
          <div class="api-module">
            <h4>🤖 策略管理</h4>
            <ul>
              <li>策略部署</li>
              <li>回测分析</li>
              <li>绩效统计</li>
            </ul>
          </div>
          <div class="api-module">
            <h4>⚠️ 风险控制</h4>
            <ul>
              <li>风险监控</li>
              <li>预警系统</li>
              <li>限额管理</li>
            </ul>
          </div>
          <div class="api-module">
            <h4>🔧 系统管理</h4>
            <ul>
              <li>健康检查</li>
              <li>监控指标</li>
              <li>日志管理</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 系统状态 -->
      <section class="system-status">
        <h2 class="section-title">⚡ 系统状态</h2>
        <div class="status-grid">
          <div class="status-card">
            <div class="status-indicator running"></div>
            <h4>后端服务</h4>
            <p>FastAPI服务正常运行</p>
            <div class="status-details">端口: 8000</div>
          </div>
          <div class="status-card">
            <div class="status-indicator running"></div>
            <h4>数据库</h4>
            <p>SQLite数据库连接正常</p>
            <div class="status-details">32张表</div>
          </div>
          <div class="status-card">
            <div class="status-indicator running"></div>
            <h4>WebSocket</h4>
            <p>实时通信服务正常</p>
            <div class="status-details">已连接</div>
          </div>
          <div class="status-card">
            <div class="status-indicator warning"></div>
            <h4>单元测试</h4>
            <p>测试框架需要修复</p>
            <div class="status-details">待优化</div>
          </div>
        </div>
      </section>

      <!-- 下一步计划 -->
      <section class="next-steps">
        <h2 class="section-title">🎯 下一步计划</h2>
        <div class="steps-grid">
          <div class="step-card">
            <div class="step-number">1</div>
            <h4>单元测试修复</h4>
            <p>完善测试框架，提高测试覆盖率，确保代码质量</p>
          </div>
          <div class="step-card">
            <div class="step-number">2</div>
            <h4>Docker部署</h4>
            <p>优化Docker配置，实现一键部署，支持生产环境</p>
          </div>
          <div class="step-card">
            <div class="step-number">3</div>
            <h4>性能优化</h4>
            <p>数据库查询优化，缓存策略，提升系统性能</p>
          </div>
          <div class="step-card">
            <div class="step-number">4</div>
            <h4>功能完善</h4>
            <p>增加更多交易品种，完善风控策略，优化用户体验</p>
          </div>
        </div>
      </section>
    </div>

    <!-- 项目链接 -->
    <div class="project-links">
      <el-button type="primary" @click="openApiDocs">
        <el-icon><Document /></el-icon>
        API文档
      </el-button>
      <el-button type="success" @click="openTradingTerminal">
        <el-icon><TrendCharts /></el-icon>
        交易终端
      </el-button>
      <el-button type="info" @click="openBacktest">
        <el-icon><DataAnalysis /></el-icon>
        策略回测
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Document, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const openApiDocs = () => {
  window.open('http://127.0.0.1:8000/docs', '_blank')
}

const openTradingTerminal = () => {
  router.push('/trading')
}

const openBacktest = () => {
  router.push('/backtest')
}
</script>

<style scoped>
.project-showcase {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.showcase-header {
  text-align: center;
  color: white;
  margin-bottom: 60px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 40px;
}

.project-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.8;
}

.showcase-content {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 2.5rem;
  color: white;
  text-align: center;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-section {
  margin-bottom: 80px;
}

.feature-category {
  margin-bottom: 50px;
}

.category-title {
  font-size: 1.8rem;
  color: white;
  margin-bottom: 30px;
  text-align: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card.completed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  text-align: center;
}

.feature-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.feature-status {
  display: inline-block;
  padding: 6px 12px;
  background: #4CAF50;
  color: white;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.tech-highlights {
  margin-bottom: 80px;
}

.highlight-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.highlight-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 35px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-5px);
}

.highlight-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
}

.highlight-card h4 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.highlight-card p {
  color: #666;
  line-height: 1.6;
}

.fix-records {
  margin-bottom: 80px;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.timeline-item {
  position: relative;
  margin-bottom: 40px;
  padding-left: 80px;
}

.timeline-marker {
  position: absolute;
  left: 15px;
  top: 0;
  width: 30px;
  height: 30px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.timeline-content {
  background: rgba(255, 255, 255, 0.95);
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.timeline-content h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.timeline-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.fix-status {
  display: inline-block;
  padding: 4px 10px;
  background: #4CAF50;
  color: white;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.api-showcase {
  margin-bottom: 80px;
}

.api-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.api-module {
  background: rgba(255, 255, 255, 0.95);
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.api-module h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.api-module ul {
  list-style: none;
  padding: 0;
}

.api-module li {
  color: #666;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.api-module li:last-child {
  border-bottom: none;
}

.system-status {
  margin-bottom: 80px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin: 0 auto 15px;
}

.status-indicator.running {
  background: #4CAF50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.status-indicator.warning {
  background: #FF9800;
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
}

.status-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.status-card p {
  color: #666;
  margin-bottom: 8px;
}

.status-details {
  font-size: 0.9rem;
  color: #999;
}

.next-steps {
  margin-bottom: 80px;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.step-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 20px 0 15px;
  color: #333;
}

.step-card p {
  color: #666;
  line-height: 1.6;
}

.project-links {
  text-align: center;
  margin-top: 60px;
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.project-links .el-button {
  padding: 12px 24px;
  font-size: 1.1rem;
  border-radius: 25px;
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .project-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .feature-grid,
  .highlight-grid,
  .api-grid,
  .status-grid,
  .steps-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline::before {
    left: 15px;
  }
  
  .timeline-marker {
    left: 0;
  }
  
  .timeline-item {
    padding-left: 60px;
  }
}
</style> 