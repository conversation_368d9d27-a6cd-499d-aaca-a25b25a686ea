# 测试环境 Docker Compose 配置
# 使用方式: docker compose -f docker/compose/staging/docker-compose.yml --env-file .env.staging up -d
version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: quant-nginx-staging
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../../nginx/staging/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ../../../ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端测试服务
  frontend:
    build:
      context: ../../..
      dockerfile: frontend/Dockerfile.prod
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-/api/v1}
        VITE_WS_URL: ${VITE_WS_URL:-/ws}
        VITE_APP_TITLE: ${VITE_APP_TITLE:-量化投资平台 (测试)}
        VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0-staging}
    container_name: quant-frontend-staging
    restart: unless-stopped
    expose:
      - "80"
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端测试服务
  backend:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-backend-staging
    restart: unless-stopped
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=staging
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DEBUG=${DEBUG:-false}
    volumes:
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant-postgres-staging
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../../backups:/backups
    networks:
      - quant-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: quant-redis-staging
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Celery Worker
  celery-worker:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-celery-worker-staging
    restart: unless-stopped
    environment:
      - ENVIRONMENT=staging
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=2

  # Celery Beat
  celery-beat:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile.prod
    container_name: quant-celery-beat-staging
    restart: unless-stopped
    environment:
      - ENVIRONMENT=staging
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ../../../data:/app/data
      - ../../../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant-network
    command: celery -A app.tasks.celery_app beat --loglevel=info

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  quant-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
