#!/bin/bash

# Docker快速启动脚本
# 使用方式: ./docker-start.sh [mode]
# mode: dev | prod | simple

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "Usage: ./docker-start.sh [mode] [action]"
    echo ""
    echo "Modes:"
    echo "  dev     - 开发模式 (只启动数据库和Redis)"
    echo "  prod    - 生产模式 (启动所有服务)"
    echo "  simple  - 简单模式 (前后端+基础服务)"
    echo ""
    echo "Actions:"
    echo "  up      - 启动服务 (默认)"
    echo "  down    - 停止服务"
    echo "  restart - 重启服务"
    echo "  logs    - 查看日志"
    echo "  ps      - 查看服务状态"
    echo ""
}

# 检查Docker和Docker Compose
check_requirements() {
    print_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        if ! docker compose version &> /dev/null; then
            print_error "Docker Compose未安装"
            exit 1
        fi
        # 使用新版docker compose命令
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    print_success "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p data/historical data/realtime data/reports data/uploads
    mkdir -p backend/logs
    print_success "目录创建完成"
}

# 生成环境变量文件
create_env_file() {
    if [ ! -f .env ]; then
        print_info "创建环境变量文件..."
        cat > .env << EOF
# 数据库配置
DATABASE_URL=postgresql://quant_user:quant_password@localhost:5432/quant_platform
POSTGRES_USER=quant_user
POSTGRES_PASSWORD=quant_password
POSTGRES_DB=quant_platform

# Redis配置
REDIS_URL=redis://localhost:6379

# 应用配置
SECRET_KEY=$(openssl rand -hex 32)
ENVIRONMENT=development
DEBUG=True

# API配置
API_BASE_URL=http://localhost:8000
FRONTEND_URL=http://localhost

# 日志配置
LOG_LEVEL=INFO
EOF
        print_success "环境变量文件创建完成"
    else
        print_info "环境变量文件已存在"
    fi
}

# 启动开发模式
start_dev() {
    print_info "启动开发模式..."
    $COMPOSE_CMD -f docker-compose.dev.yml up -d
    print_success "开发环境启动完成"
    echo ""
    echo "服务地址:"
    echo "  PostgreSQL: localhost:5432"
    echo "  Redis: localhost:6379"
    echo "  pgAdmin: http://localhost:5050"
    echo "  RedisInsight: http://localhost:8081"
}

# 启动生产模式
start_prod() {
    print_info "启动生产模式..."
    $COMPOSE_CMD up -d
    print_success "生产环境启动完成"
    echo ""
    echo "服务地址:"
    echo "  前端: http://localhost"
    echo "  后端API: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
    echo "  pgAdmin: http://localhost:5050"
    echo "  RedisInsight: http://localhost:8081"
}

# 启动简单模式
start_simple() {
    print_info "启动简单模式..."
    $COMPOSE_CMD up -d postgres redis backend frontend
    print_success "简单模式启动完成"
    echo ""
    echo "服务地址:"
    echo "  前端: http://localhost"
    echo "  后端API: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    if [ "$MODE" = "dev" ]; then
        $COMPOSE_CMD -f docker-compose.dev.yml down
    else
        $COMPOSE_CMD down
    fi
    print_success "服务已停止"
}

# 查看日志
show_logs() {
    if [ "$MODE" = "dev" ]; then
        $COMPOSE_CMD -f docker-compose.dev.yml logs -f
    else
        $COMPOSE_CMD logs -f
    fi
}

# 查看服务状态
show_status() {
    if [ "$MODE" = "dev" ]; then
        $COMPOSE_CMD -f docker-compose.dev.yml ps
    else
        $COMPOSE_CMD ps
    fi
}

# 主函数
main() {
    MODE=${1:-simple}
    ACTION=${2:-up}
    
    case $MODE in
        dev|prod|simple)
            ;;
        -h|--help|help)
            show_usage
            exit 0
            ;;
        *)
            print_error "未知模式: $MODE"
            show_usage
            exit 1
            ;;
    esac
    
    check_requirements
    
    case $ACTION in
        up)
            create_directories
            create_env_file
            case $MODE in
                dev)
                    start_dev
                    ;;
                prod)
                    start_prod
                    ;;
                simple)
                    start_simple
                    ;;
            esac
            ;;
        down)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            case $MODE in
                dev)
                    start_dev
                    ;;
                prod)
                    start_prod
                    ;;
                simple)
                    start_simple
                    ;;
            esac
            ;;
        logs)
            show_logs
            ;;
        ps)
            show_status
            ;;
        *)
            print_error "未知操作: $ACTION"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"