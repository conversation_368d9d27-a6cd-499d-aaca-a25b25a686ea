# 生产环境配置模板
# 复制此文件为 .env.prod 并填入实际的生产环境值

# =============================================================================
# 应用基础配置
# =============================================================================
APP_ENV=production
APP_NAME=量化投资平台
APP_VERSION=1.0.0
DEBUG=false

# =============================================================================
# 数据库配置 (生产环境)
# =============================================================================
POSTGRES_DB=quantplatform_prod
POSTGRES_USER=quantuser
POSTGRES_PASSWORD=CHANGE_ME_STRONG_PASSWORD_HERE
DATABASE_URL=*******************************************************************/quantplatform_prod

# =============================================================================
# Redis 配置 (生产环境)
# =============================================================================
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# =============================================================================
# 认证配置 (生产环境 - 必须更改)
# =============================================================================
SECRET_KEY=CHANGE_ME_RANDOM_SECRET_KEY_64_CHARS_MINIMUM_FOR_PRODUCTION_USE
JWT_SECRET_KEY=CHANGE_ME_RANDOM_JWT_SECRET_KEY_64_CHARS_MINIMUM_FOR_PRODUCTION
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# CORS 配置 (生产环境)
# =============================================================================
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# 前端配置 (生产环境)
# =============================================================================
VITE_API_BASE_URL=/api/v1
VITE_WS_URL=/ws
VITE_APP_TITLE=量化投资平台
VITE_APP_VERSION=1.0.0

# =============================================================================
# 日志配置 (生产环境)
# =============================================================================
LOG_LEVEL=WARNING
LOG_FILE_PATH=logs/app.log

# =============================================================================
# 监控配置 (生产环境)
# =============================================================================
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# =============================================================================
# 邮件配置 (生产环境)
# =============================================================================
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=CHANGE_ME_SMTP_PASSWORD
SMTP_USE_TLS=true
MAIL_FROM_NAME=量化投资平台
MAIL_FROM_EMAIL=<EMAIL>

# =============================================================================
# 数据源配置 (生产环境)
# =============================================================================
TUSHARE_TOKEN=YOUR_PRODUCTION_TUSHARE_TOKEN
AKSHARE_ENABLED=true

# =============================================================================
# CTP 配置 (生产环境)
# =============================================================================
CTP_BROKER_ID=YOUR_BROKER_ID
CTP_INVESTOR_ID=YOUR_INVESTOR_ID
CTP_PASSWORD=YOUR_CTP_PASSWORD
CTP_TRADE_FRONT=tcp://your-trade-server:port
CTP_MARKET_FRONT=tcp://your-market-server:port
CTP_AUTH_CODE=YOUR_AUTH_CODE
CTP_USER_PRODUCT_INFO=YOUR_PRODUCT_INFO

# =============================================================================
# 性能配置 (生产环境)
# =============================================================================
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=100
MARKET_DATA_CACHE_TIMEOUT=60
HISTORICAL_DATA_CACHE_TIMEOUT=3600

# =============================================================================
# 风险管理配置 (生产环境)
# =============================================================================
MAX_POSITION_SIZE=1000000
MAX_DAILY_LOSS=50000
MAX_DRAWDOWN=0.15
RISK_MONITORING_ENABLED=true
RISK_ALERT_EMAIL=<EMAIL>

# =============================================================================
# 备份配置 (生产环境)
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# =============================================================================
# SSL/TLS 配置 (生产环境)
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# 云服务配置 (生产环境 - 可选)
# =============================================================================
# 阿里云
ALIYUN_ACCESS_KEY_ID=YOUR_ALIYUN_ACCESS_KEY_ID
ALIYUN_ACCESS_KEY_SECRET=YOUR_ALIYUN_ACCESS_KEY_SECRET

# 腾讯云
TENCENT_SECRET_ID=YOUR_TENCENT_SECRET_ID
TENCENT_SECRET_KEY=YOUR_TENCENT_SECRET_KEY

# AWS (如果使用)
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY
AWS_REGION=us-west-2

# =============================================================================
# 其他配置
# =============================================================================
TIMEZONE=Asia/Shanghai
LANGUAGE=zh-CN
MAX_UPLOAD_SIZE=10485760
UPLOAD_PATH=uploads/
STATIC_FILES_PATH=static/

# =============================================================================
# 安全配置 (生产环境)
# =============================================================================
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
SECURE_SSL_REDIRECT=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
