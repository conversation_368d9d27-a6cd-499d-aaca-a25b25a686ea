/**
 * 前端状态检查脚本
 * 验证前端主要页面和功能是否正常工作
 */

const puppeteer = require('puppeteer');

const FRONTEND_URL = 'http://localhost:5174';
const BACKEND_URL = 'http://localhost:8000';

// 要检查的页面列表
const PAGES_TO_CHECK = [
  { path: '/', name: '首页' },
  { path: '/login', name: '登录页' },
  { path: '/dashboard', name: '仪表盘' },
  { path: '/market', name: '市场数据' },
  { path: '/trading', name: '交易终端' },
  { path: '/strategy', name: '策略中心' },
  { path: '/backtest', name: '回测分析' },
  { path: '/portfolio', name: '投资组合' },
  { path: '/risk', name: '风险管理' },
  { path: '/api-test', name: 'API测试' },
  { path: '/demo', name: '组件展示' }
];

async function checkFrontendStatus() {
  console.log('🚀 开始检查前端状态...\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1280, height: 720 }
  });
  
  const page = await browser.newPage();
  
  // 设置超时时间
  page.setDefaultTimeout(10000);
  
  const results = {
    success: [],
    failed: [],
    warnings: []
  };
  
  // 检查后端健康状态
  console.log('📡 检查后端连接...');
  try {
    const response = await fetch(`${BACKEND_URL}/health`);
    if (response.ok) {
      console.log('✅ 后端服务正常运行');
      results.success.push('后端健康检查');
    } else {
      console.log('⚠️ 后端服务响应异常');
      results.warnings.push('后端服务响应异常');
    }
  } catch (error) {
    console.log('❌ 后端服务无法连接');
    results.failed.push('后端服务连接失败');
  }
  
  // 检查前端页面
  console.log('\n📄 检查前端页面...');
  
  for (const pageInfo of PAGES_TO_CHECK) {
    try {
      console.log(`检查页面: ${pageInfo.name} (${pageInfo.path})`);
      
      const url = `${FRONTEND_URL}${pageInfo.path}`;
      await page.goto(url, { waitUntil: 'networkidle0' });
      
      // 检查页面是否有错误
      const hasError = await page.evaluate(() => {
        const errorElements = document.querySelectorAll('.error, .el-message--error, [class*="error"]');
        return errorElements.length > 0;
      });
      
      // 检查页面是否有内容
      const hasContent = await page.evaluate(() => {
        return document.body.innerText.trim().length > 0;
      });
      
      if (hasError) {
        console.log(`⚠️ ${pageInfo.name} - 页面有错误提示`);
        results.warnings.push(`${pageInfo.name} - 页面有错误提示`);
      } else if (!hasContent) {
        console.log(`❌ ${pageInfo.name} - 页面内容为空`);
        results.failed.push(`${pageInfo.name} - 页面内容为空`);
      } else {
        console.log(`✅ ${pageInfo.name} - 页面正常加载`);
        results.success.push(`${pageInfo.name} - 页面正常加载`);
      }
      
      // 等待一下避免请求过快
      await page.waitForTimeout(1000);
      
    } catch (error) {
      console.log(`❌ ${pageInfo.name} - 加载失败: ${error.message}`);
      results.failed.push(`${pageInfo.name} - 加载失败: ${error.message}`);
    }
  }
  
  await browser.close();
  
  // 输出总结
  console.log('\n📊 检查结果总结:');
  console.log(`✅ 成功: ${results.success.length} 项`);
  console.log(`⚠️ 警告: ${results.warnings.length} 项`);
  console.log(`❌ 失败: ${results.failed.length} 项`);
  
  if (results.failed.length > 0) {
    console.log('\n❌ 失败项目:');
    results.failed.forEach(item => console.log(`  - ${item}`));
  }
  
  if (results.warnings.length > 0) {
    console.log('\n⚠️ 警告项目:');
    results.warnings.forEach(item => console.log(`  - ${item}`));
  }
  
  console.log('\n✅ 成功项目:');
  results.success.forEach(item => console.log(`  - ${item}`));
  
  return results;
}

// 运行检查
if (require.main === module) {
  checkFrontendStatus()
    .then(results => {
      const totalIssues = results.failed.length + results.warnings.length;
      if (totalIssues === 0) {
        console.log('\n🎉 前端状态检查完成，所有功能正常！');
        process.exit(0);
      } else {
        console.log(`\n⚠️ 前端状态检查完成，发现 ${totalIssues} 个问题需要处理`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ 检查过程中发生错误:', error);
      process.exit(1);
    });
}

module.exports = { checkFrontendStatus };
