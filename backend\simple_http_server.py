"""
简单的HTTP服务器用于测试
增加了缓存机制和性能优化
"""

import json
import http.server
import socketserver
import time
import random
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any

# 简单的内存缓存
cache: Dict[str, Dict[str, Any]] = {}
CACHE_TTL = 30  # 缓存30秒

class SimpleHTTPRequestHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()
        
        # 路由处理
        if path == '/':
            response = {
                "message": "量化投资平台API",
                "version": "1.0.0",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
        elif path == '/health':
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "services": {
                    "api": "running",
                    "database": "connected"
                }
            }
        elif path == '/api/v1/market/stocks':
            response = {
                "success": True,
                "data": [
                    {
                        "symbol": "000001.SZ",
                        "name": "平安银行",
                        "price": 12.50,
                        "change": 0.15,
                        "change_percent": 1.22
                    },
                    {
                        "symbol": "000002.SZ",
                        "name": "万科A",
                        "price": 18.30,
                        "change": -0.25,
                        "change_percent": -1.35
                    },
                    {
                        "symbol": "600000.SH",
                        "name": "浦发银行",
                        "price": 8.95,
                        "change": 0.08,
                        "change_percent": 0.90
                    },
                    {
                        "symbol": "000858.SZ",
                        "name": "五粮液",
                        "price": 128.50,
                        "change": 2.30,
                        "change_percent": 1.82
                    }
                ],
                "timestamp": datetime.now().isoformat()
            }
        elif path.startswith('/api/v1/market/quote/'):
            symbol = path.split('/')[-1]
            response = {
                "success": True,
                "data": {
                    "symbol": symbol,
                    "name": f"股票{symbol}",
                    "price": 25.68,
                    "open": 25.50,
                    "high": 26.00,
                    "low": 25.20,
                    "volume": 1234567,
                    "change": 0.18,
                    "change_percent": 0.71,
                    "timestamp": datetime.now().isoformat()
                }
            }
        else:
            response = {
                "error": "Not Found",
                "path": path,
                "timestamp": datetime.now().isoformat()
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()

if __name__ == "__main__":
    PORT = 8000
    with socketserver.TCPServer(("", PORT), SimpleHTTPRequestHandler) as httpd:
        print(f"服务器启动在端口 {PORT}")
        print(f"访问 http://localhost:{PORT}")
        print(f"健康检查: http://localhost:{PORT}/health")
        httpd.serve_forever()
