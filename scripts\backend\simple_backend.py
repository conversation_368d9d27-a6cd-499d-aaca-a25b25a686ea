#!/usr/bin/env python3
"""
简化的量化投资平台后端服务
用于快速测试前后端连接
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 创建应用实例
app = FastAPI(
    title="量化投资平台 API",
    description="专业的量化投资后端服务",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
MOCK_STOCKS = [
    {"symbol": "000001.SZ", "name": "平安银行", "price": 12.45, "change": 0.23, "change_percent": 1.88, "volume": 125634000},
    {"symbol": "000002.SZ", "name": "万科A", "price": 18.67, "change": -0.34, "change_percent": -1.79, "volume": 89234000},
    {"symbol": "000858.SZ", "name": "五粮液", "price": 189.50, "change": 2.10, "change_percent": 1.12, "volume": 45678000},
    {"symbol": "300750.SZ", "name": "宁德时代", "price": 225.80, "change": -5.20, "change_percent": -2.25, "volume": 78912000},
    {"symbol": "600036.SH", "name": "招商银行", "price": 42.15, "change": 0.85, "change_percent": 2.06, "volume": 98765000},
]

# 模拟策略数据
MOCK_STRATEGIES = [
    {
        "id": 1,
        "name": "均线策略",
        "description": "基于移动平均线的交易策略",
        "type": "trend_following",
        "status": "active",
        "return_rate": 15.6,
        "max_drawdown": -8.2,
        "sharpe_ratio": 1.45,
        "created_at": "2024-01-15T10:00:00Z",
        "updated_at": "2024-08-07T15:30:00Z"
    },
    {
        "id": 2,
        "name": "RSI反转策略",
        "description": "基于RSI指标的反转交易策略",
        "type": "mean_reversion",
        "status": "active",
        "return_rate": 12.3,
        "max_drawdown": -6.5,
        "sharpe_ratio": 1.28,
        "created_at": "2024-02-20T14:00:00Z",
        "updated_at": "2024-08-07T15:30:00Z"
    },
    {
        "id": 3,
        "name": "动量突破策略",
        "description": "基于价格动量的突破策略",
        "type": "momentum",
        "status": "paused",
        "return_rate": 8.9,
        "max_drawdown": -12.1,
        "sharpe_ratio": 0.95,
        "created_at": "2024-03-10T09:00:00Z",
        "updated_at": "2024-08-07T15:30:00Z"
    }
]

# WebSocket连接管理
active_connections = []

@app.get("/")
async def root():
    return {"message": "量化投资平台 API", "status": "running", "version": "1.0.0"}

@app.get("/api/v1/health")
async def health_check():
    return {"code": 200, "message": "success", "data": {"status": "healthy", "timestamp": datetime.now().isoformat()}}

@app.post("/api/v1/auth/login")
async def login(request: dict):
    username = request.get("username", "")
    password = request.get("password", "")
    
    if username == "admin" and password == "admin123":
        return {
            "code": 200,
            "message": "success",
            "data": {
                "access_token": "mock_token_12345",
                "token_type": "bearer",
                "expires_in": 7200,
                "user": {"id": 1, "username": "admin", "email": "<EMAIL>"}
            }
        }
    else:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

@app.get("/api/v1/auth/profile")
async def get_profile():
    return {
        "code": 200,
        "message": "success",
        "data": {"id": 1, "username": "admin", "email": "<EMAIL>", "is_active": True}
    }

@app.get("/api/v1/market/overview")
async def get_market_overview():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "total_stocks": len(MOCK_STOCKS),
            "active_stocks": len(MOCK_STOCKS),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/v1/market/stocks")
async def get_stocks(limit: int = 20, offset: int = 0):
    stocks = MOCK_STOCKS[offset:offset+limit]
    for stock in stocks:
        stock["timestamp"] = datetime.now().isoformat()
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "items": stocks,
            "total": len(MOCK_STOCKS),
            "page": offset // limit + 1,
            "pages": (len(MOCK_STOCKS) + limit - 1) // limit
        }
    }

@app.get("/api/v1/market/quote/{symbol}")
async def get_quote(symbol: str):
    stock = next((s for s in MOCK_STOCKS if s["symbol"] == symbol), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    stock_copy = stock.copy()
    stock_copy["timestamp"] = datetime.now().isoformat()
    return {"code": 200, "message": "success", "data": stock_copy}

@app.get("/api/v1/strategies")
async def get_strategies():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "items": MOCK_STRATEGIES,
            "total": len(MOCK_STRATEGIES)
        }
    }

@app.get("/api/v1/strategies/{strategy_id}")
async def get_strategy(strategy_id: int):
    strategy = next((s for s in MOCK_STRATEGIES if s["id"] == strategy_id), None)
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    return {"code": 200, "message": "success", "data": strategy}

@app.get("/api/v1/trading/account")
async def get_account():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "account_id": "demo_account",
            "balance": 1000000.0,
            "available": 800000.0,
            "frozen": 200000.0,
            "total_asset": 1200000.0,
            "market_value": 200000.0,
            "profit_loss": 50000.0,
            "profit_loss_percent": 4.17
        }
    }

@app.get("/api/v1/trading/positions")
async def get_positions():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "items": [
                {
                    "symbol": "000001.SZ",
                    "name": "平安银行",
                    "quantity": 1000,
                    "available": 1000,
                    "avg_price": 12.20,
                    "current_price": 12.45,
                    "market_value": 12450.0,
                    "profit_loss": 250.0,
                    "profit_loss_percent": 2.05
                }
            ],
            "total": 1
        }
    }

@app.get("/api/v1/trading/orders")
async def get_orders():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "items": [
                {
                    "order_id": "20240801001",
                    "symbol": "000001.SZ",
                    "name": "平安银行",
                    "side": "buy",
                    "order_type": "limit",
                    "quantity": 1000,
                    "price": 12.20,
                    "filled_quantity": 1000,
                    "avg_price": 12.20,
                    "status": "filled",
                    "create_time": datetime.now().isoformat()
                }
            ],
            "total": 1
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
