import { createRouter, createWebHistory } from 'vue-router'
import dashboardRoutes from './modules/dashboard'
import marketRoutes from './modules/market'
import tradingRoutes from './modules/trading'
import strategyRoutes from './modules/strategy'
import backtestRoutes from './modules/backtest'
import portfolioRoutes from './modules/portfolio'
import riskRoutes from './modules/risk'
import settingsRoutes from './modules/settings'
import errorRoutes from './modules/error'
import notificationRoutes from './modules/notification'
import { setupRouterGuards } from './guards'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('@/layouts/DefaultLayout.vue'),
      redirect: '/dashboard',
      children: [
        ...dashboardRoutes,
        ...marketRoutes,
        ...tradingRoutes,
        ...strategyRoutes,
        ...backtestRoutes,
        ...portfolioRoutes,
        ...riskRoutes,
        ...settingsRoutes,
        ...notificationRoutes,
        // 组件展示页面
        {
          path: '/demo',
          name: 'ComponentShowcase',
          component: () => import('@/views/Demo/ComponentShowcase.vue'),
          meta: {
            title: '组件展示',
            icon: 'Grid',
            requiresAuth: false
          }
        },
        // API测试页面
        {
          path: '/api-test',
          name: 'ApiTest',
          component: () => import('@/views/Test/ApiTest.vue'),
          meta: {
            title: 'API测试',
            icon: 'Connection',
            requiresAuth: false
          }
        },
        // 错误测试页面
        {
          path: '/error-test',
          name: 'ErrorTest',
          component: () => import('@/views/test/ErrorTest.vue'),
          meta: {
            title: '错误测试',
            icon: 'Warning',
            requiresAuth: false
          }
        },
        // API调试页面
        {
          path: '/api-debug',
          name: 'ApiDebug',
          component: () => import('@/views/Test/ApiDebug.vue'),
          meta: {
            title: 'API调试',
            icon: 'Connection',
            requiresAuth: false
          }
        },
      ],
    },
    // 认证相关路由
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Auth/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false,
      },
    },
    {
      path: '/slide-verify',
      name: 'SlideVerify',
      component: () => import('@/views/Auth/SlideVerifyView.vue'),
      meta: {
        title: '滑块验证',
        requiresAuth: false,
      },
    },
    {
      path: '/test-slider',
      name: 'TestSlider',
      component: () => import('@/views/Auth/TestSlider.vue'),
      meta: {
        title: '滑轨验证测试',
        requiresAuth: false,
      },
    },
    {
      path: '/test-captcha',
      name: 'TestCaptcha',
      component: () => import('@/views/TestCaptcha.vue'),
      meta: {
        title: '滑动验证码测试',
        requiresAuth: false,
      },
    },
    {
      path: '/puzzle-verify',
      name: 'PuzzleVerify',
      component: () => import('@/views/PuzzleVerifyView.vue'),
      meta: {
        title: '拼图验证',
        requiresAuth: false,
      },
    },
    {
      path: '/test-puzzle',
      name: 'TestPuzzle',
      component: () => import('@/views/TestPuzzle.vue'),
      meta: {
        title: '测试拼图',
        requiresAuth: false,
      },
    },
    {
      path: '/puzzle-simple',
      name: 'PuzzleSimple',
      component: () => import('@/views/PuzzleVerifySimple.vue'),
      meta: {
        title: '简化拼图验证',
        requiresAuth: false,
      },
    },
    // 错误页面路由
    ...errorRoutes,
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router

