#!/usr/bin/env python3
"""
量化投资平台关键问题修复脚本
自动修复P0和P1级别的关键问题
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Dict, Any
import json
import re

class ProjectFixer:
    """项目修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.fixes_applied = []
        self.errors = []
        
    def log_fix(self, fix_name: str, status: str, details: str = ""):
        """记录修复操作"""
        self.fixes_applied.append({
            "fix": fix_name,
            "status": status,
            "details": details
        })
        print(f"{'✅' if status == 'success' else '❌'} {fix_name}: {details}")
    
    def fix_package_management_conflicts(self):
        """修复包管理冲突"""
        print("\n🔧 修复包管理冲突...")
        
        frontend_dir = self.project_root / "frontend"
        package_lock = frontend_dir / "package-lock.json"
        pnpm_lock = frontend_dir / "pnpm-lock.yaml"
        
        if package_lock.exists() and pnpm_lock.exists():
            try:
                package_lock.unlink()
                self.log_fix("删除package-lock.json", "success", "解决pnpm/npm冲突")
            except Exception as e:
                self.log_fix("删除package-lock.json", "error", str(e))
        
        # 重新安装前端依赖
        if pnpm_lock.exists():
            try:
                os.chdir(frontend_dir)
                subprocess.run(["pnpm", "install"], check=True, capture_output=True)
                self.log_fix("重新安装前端依赖", "success", "使用pnpm")
                os.chdir(self.project_root)
            except subprocess.CalledProcessError as e:
                self.log_fix("重新安装前端依赖", "error", f"pnpm install失败: {e}")
                os.chdir(self.project_root)
            except FileNotFoundError:
                self.log_fix("重新安装前端依赖", "warning", "pnpm未安装，跳过")
    
    def clean_build_artifacts(self):
        """清理构建产物"""
        print("\n🧹 清理构建产物...")
        
        artifacts_to_clean = [
            "frontend/dist",
            "frontend/node_modules",
            "backend/__pycache__",
            "**/__pycache__",
            "**/*.pyc",
            "**/*.pyo",
            "**/*.pyd",
            "logs/*.log",
        ]
        
        for pattern in artifacts_to_clean:
            try:
                if "*" in pattern:
                    # 使用glob模式
                    for path in self.project_root.glob(pattern):
                        if path.is_file():
                            path.unlink()
                        elif path.is_dir():
                            shutil.rmtree(path)
                else:
                    path = self.project_root / pattern
                    if path.exists():
                        if path.is_file():
                            path.unlink()
                        elif path.is_dir():
                            shutil.rmtree(path)
                        self.log_fix(f"清理{pattern}", "success", "已删除")
            except Exception as e:
                self.log_fix(f"清理{pattern}", "error", str(e))
    
    def update_gitignore(self):
        """更新.gitignore文件"""
        print("\n📝 更新.gitignore...")
        
        gitignore_content = """
# 构建产物
frontend/dist/
frontend/node_modules/
backend/dist/

# Python
__pycache__/
*.py[cod]
*.so
*.egg-info/
.pytest_cache/
.coverage
htmlcov/

# 日志文件
*.log
logs/
backend/logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 环境变量
.env
.env.local
.env.*.local
.env.development
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
*.tmp
*.temp
.cache/

# 依赖包
node_modules/
venv/
env/

# 测试覆盖率
coverage/
.nyc_output/

# 构建工具
.parcel-cache/
.next/
.nuxt/
"""
        
        gitignore_path = self.project_root / ".gitignore"
        try:
            with open(gitignore_path, "w", encoding="utf-8") as f:
                f.write(gitignore_content.strip())
            self.log_fix("更新.gitignore", "success", "添加了完整的忽略规则")
        except Exception as e:
            self.log_fix("更新.gitignore", "error", str(e))
    
    def fix_backend_dependencies(self):
        """修复后端依赖冲突"""
        print("\n🐍 修复后端依赖冲突...")
        
        requirements_path = self.project_root / "backend" / "requirements.txt"
        
        if not requirements_path.exists():
            self.log_fix("修复requirements.txt", "warning", "文件不存在")
            return
        
        try:
            with open(requirements_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 修复已知冲突
            fixes = [
                # 移除重复的cryptography版本
                (r'cryptography>=.*\n', ''),
                # 移除aioredis（已合并到redis-py）
                (r'aioredis==.*\n', ''),
                # 确保redis版本
                (r'redis==.*\n', 'redis==5.0.1\n'),
            ]
            
            original_content = content
            for pattern, replacement in fixes:
                content = re.sub(pattern, replacement, content)
            
            if content != original_content:
                with open(requirements_path, "w", encoding="utf-8") as f:
                    f.write(content)
                self.log_fix("修复requirements.txt", "success", "解决依赖冲突")
            else:
                self.log_fix("修复requirements.txt", "info", "无需修复")
                
        except Exception as e:
            self.log_fix("修复requirements.txt", "error", str(e))
    
    def remove_empty_directories(self):
        """删除空目录"""
        print("\n📁 删除空目录...")
        
        empty_dirs = [
            "backend/app/events",
            "backend/app/constants", 
            "backend/app/workers",
            "backend/app/websocket",
            "backend/app/api/v1/websocket/ws",
        ]
        
        for dir_path in empty_dirs:
            path = self.project_root / dir_path
            try:
                if path.exists() and path.is_dir():
                    # 检查是否为空目录
                    if not any(path.iterdir()):
                        path.rmdir()
                        self.log_fix(f"删除空目录{dir_path}", "success", "已删除")
                    else:
                        self.log_fix(f"删除空目录{dir_path}", "info", "目录非空，跳过")
            except Exception as e:
                self.log_fix(f"删除空目录{dir_path}", "error", str(e))
    
    def fix_api_routes(self):
        """修复API路由注册"""
        print("\n🌐 检查API路由注册...")
        
        api_init_path = self.project_root / "backend" / "app" / "api" / "v1" / "__init__.py"
        
        if not api_init_path.exists():
            self.log_fix("检查API路由", "warning", "__init__.py不存在")
            return
        
        try:
            with open(api_init_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 检查是否包含必要的路由
            required_routes = [
                "monitoring",
                "storage", 
                "market",
                "trading",
                "auth"
            ]
            
            missing_routes = []
            for route in required_routes:
                if f"from .{route}" not in content and f"{route}_router" not in content:
                    missing_routes.append(route)
            
            if missing_routes:
                self.log_fix("检查API路由", "warning", f"可能缺失路由: {missing_routes}")
            else:
                self.log_fix("检查API路由", "success", "路由注册完整")
                
        except Exception as e:
            self.log_fix("检查API路由", "error", str(e))
    
    def organize_docker_configs(self):
        """整理Docker配置"""
        print("\n🐳 整理Docker配置...")
        
        # 创建标准目录结构
        docker_dir = self.project_root / "docker"
        compose_dir = docker_dir / "compose"
        
        try:
            compose_dir.mkdir(parents=True, exist_ok=True)
            
            # 移动分散的compose文件
            compose_files = [
                "docker-compose.yml",
                "docker-compose.dev.yml", 
                "docker-compose.prod.yml",
                "docker-compose.local.yml",
                "docker-compose.production.yml"
            ]
            
            moved_count = 0
            for filename in compose_files:
                source = self.project_root / filename
                if source.exists():
                    target = compose_dir / filename
                    shutil.move(str(source), str(target))
                    moved_count += 1
            
            if moved_count > 0:
                self.log_fix("整理Docker配置", "success", f"移动了{moved_count}个配置文件")
            else:
                self.log_fix("整理Docker配置", "info", "无需移动文件")
                
        except Exception as e:
            self.log_fix("整理Docker配置", "error", str(e))
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n📊 生成修复报告...")
        
        report = {
            "fix_time": str(Path.cwd()),
            "total_fixes": len(self.fixes_applied),
            "successful_fixes": len([f for f in self.fixes_applied if f["status"] == "success"]),
            "failed_fixes": len([f for f in self.fixes_applied if f["status"] == "error"]),
            "fixes": self.fixes_applied
        }
        
        report_path = self.project_root / "fix_report.json"
        try:
            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📄 修复报告已保存到: {report_path}")
        except Exception as e:
            print(f"❌ 保存修复报告失败: {e}")
        
        # 打印摘要
        print(f"\n📈 修复摘要:")
        print(f"   总修复项: {report['total_fixes']}")
        print(f"   成功修复: {report['successful_fixes']}")
        print(f"   修复失败: {report['failed_fixes']}")
        
        if report['failed_fixes'] > 0:
            print(f"\n❌ 失败的修复项:")
            for fix in self.fixes_applied:
                if fix["status"] == "error":
                    print(f"   - {fix['fix']}: {fix['details']}")
    
    def run_all_fixes(self):
        """运行所有修复"""
        print("🚀 开始修复量化投资平台关键问题...")
        print(f"📁 项目路径: {self.project_root}")
        
        # 按优先级执行修复
        self.fix_package_management_conflicts()
        self.clean_build_artifacts()
        self.update_gitignore()
        self.fix_backend_dependencies()
        self.remove_empty_directories()
        self.fix_api_routes()
        self.organize_docker_configs()
        
        # 生成报告
        self.generate_fix_report()
        
        print("\n🎉 修复完成！")
        print("💡 建议接下来:")
        print("   1. 检查修复报告")
        print("   2. 测试应用启动")
        print("   3. 运行测试套件")
        print("   4. 提交代码更改")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    fixer = ProjectFixer(project_root)
    fixer.run_all_fixes()

if __name__ == "__main__":
    main()
