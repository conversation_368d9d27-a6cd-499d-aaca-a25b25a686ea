# Archive目录清理计划

## 🎯 清理目标
- 移除包含8001端口的过时配置文件
- 清理自动生成的错误测试报告
- 保留有价值的历史配置作为参考

## 📋 发现的问题文件

### 1. 批处理脚本 (8001端口)
- `archive/batch_scripts/start_fixed_platform.bat` - 第22,40,41行引用8001端口
- `archive/batch_scripts/start_platform_en.bat` - 需要检查

### 2. Docker配置 (端口映射错误)
- `archive/old_configs/config_docker/docker-compose.production.yml` - 第54行端口映射8001:8000
- `archive/old_configs/docker-compose.dev.yml` - 需要检查

### 3. 测试报告 (错误端口引用)
- `archive/diagnostic_reports/comprehensive_test_report_1754558895.json` - 第12行后端端口8001
- 其他测试脚本可能也有类似问题

### 4. 自动化脚本
- `archive/test_scripts/comprehensive_final_test.py`
- `archive/automation_scripts/comprehensive_test_report_generator.js`
- `archive/logs/test_all_apis.py`

## 🗑️ 清理操作

### 立即删除的文件
1. 过时的批处理脚本 (8001端口配置)
2. 错误的Docker配置文件
3. 包含错误端口的测试报告
4. 无用的自动生成测试脚本

### 需要修正的文件
1. 更新剩余配置文件中的端口引用
2. 创建正确的端口配置说明文档

## ✅ 保留的文件
- 有教育价值的配置模板
- 重要的部署说明文档
- 架构设计相关文件