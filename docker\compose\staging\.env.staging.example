# 测试环境配置模板
# 复制此文件为 .env.staging 并填入测试环境值

# =============================================================================
# 应用基础配置
# =============================================================================
APP_ENV=staging
APP_NAME=量化投资平台 (测试)
APP_VERSION=1.0.0-staging
DEBUG=false

# =============================================================================
# 数据库配置 (测试环境)
# =============================================================================
POSTGRES_DB=quantplatform_staging
POSTGRES_USER=quantuser_staging
POSTGRES_PASSWORD=staging_password_change_me
DATABASE_URL=***********************************************************************/quantplatform_staging

# =============================================================================
# Redis 配置 (测试环境)
# =============================================================================
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# =============================================================================
# 认证配置 (测试环境)
# =============================================================================
SECRET_KEY=staging-secret-key-change-me-for-security
JWT_SECRET_KEY=staging-jwt-secret-key-change-me-for-security
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# CORS 配置 (测试环境)
# =============================================================================
CORS_ORIGINS=https://staging.yourdomain.com,https://test.yourdomain.com

# =============================================================================
# 前端配置 (测试环境)
# =============================================================================
VITE_API_BASE_URL=/api/v1
VITE_WS_URL=/ws
VITE_APP_TITLE=量化投资平台 (测试环境)
VITE_APP_VERSION=1.0.0-staging

# =============================================================================
# 日志配置 (测试环境)
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/app.log

# =============================================================================
# 监控配置 (测试环境)
# =============================================================================
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# =============================================================================
# 邮件配置 (测试环境)
# =============================================================================
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=staging_smtp_password
SMTP_USE_TLS=true
MAIL_FROM_NAME=量化投资平台 (测试)
MAIL_FROM_EMAIL=<EMAIL>

# =============================================================================
# 数据源配置 (测试环境)
# =============================================================================
TUSHARE_TOKEN=YOUR_STAGING_TUSHARE_TOKEN
AKSHARE_ENABLED=true

# =============================================================================
# CTP 配置 (测试环境)
# =============================================================================
CTP_BROKER_ID=YOUR_TEST_BROKER_ID
CTP_INVESTOR_ID=YOUR_TEST_INVESTOR_ID
CTP_PASSWORD=YOUR_TEST_CTP_PASSWORD
CTP_TRADE_FRONT=tcp://your-test-trade-server:port
CTP_MARKET_FRONT=tcp://your-test-market-server:port
CTP_AUTH_CODE=YOUR_TEST_AUTH_CODE
CTP_USER_PRODUCT_INFO=YOUR_TEST_PRODUCT_INFO

# =============================================================================
# 性能配置 (测试环境)
# =============================================================================
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=200
MARKET_DATA_CACHE_TIMEOUT=60
HISTORICAL_DATA_CACHE_TIMEOUT=1800

# =============================================================================
# 风险管理配置 (测试环境)
# =============================================================================
MAX_POSITION_SIZE=100000
MAX_DAILY_LOSS=10000
MAX_DRAWDOWN=0.2
RISK_MONITORING_ENABLED=true
RISK_ALERT_EMAIL=<EMAIL>

# =============================================================================
# 备份配置 (测试环境)
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 3 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=backups/

# =============================================================================
# SSL/TLS 配置 (测试环境)
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/staging-cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/staging-key.pem

# =============================================================================
# 其他配置
# =============================================================================
TIMEZONE=Asia/Shanghai
LANGUAGE=zh-CN
MAX_UPLOAD_SIZE=10485760
UPLOAD_PATH=uploads/
STATIC_FILES_PATH=static/

# =============================================================================
# 安全配置 (测试环境)
# =============================================================================
ALLOWED_HOSTS=staging.yourdomain.com,test.yourdomain.com
SECURE_SSL_REDIRECT=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
