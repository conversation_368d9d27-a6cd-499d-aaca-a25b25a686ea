/**
 * 简化的应用主入口文件 - 调试版本
 * 逐步加载依赖，便于定位问题
 */

import { createApp } from 'vue'

console.log('🔍 开始加载应用...')

// 创建一个简单的测试组件
const TestApp = {
  template: `
    <div style="padding: 20px; text-align: center;">
      <h1>🎉 Vue 应用加载成功！</h1>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime">更新时间</button>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString('zh-CN')
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
    }
  }
}

// 创建Vue应用实例
const app = createApp(TestApp)

// =======================
// Pinia状态管理配置
// =======================
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

// =======================
// 路由配置
// =======================
app.use(router)

// =======================
// 国际化配置
// =======================
app.use(i18n)

// =======================
// Element Plus配置
// =======================
app.use(ElementPlus)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// =======================
// 全局组件注册
// =======================
app.use(GlobalComponents)

// =======================
// 滑块验证组件
// =======================
app.use(SlideVerify)

// =======================
// 全局属性配置
// =======================
app.config.globalProperties.$echarts = echarts
app.config.globalProperties.$http = http

// =======================
// 错误处理
// =======================
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 在开发环境显示详细错误
  if (config.app.isDev) {
    console.error('组件实例:', instance)
  }
}

// Vue警告处理
app.config.warnHandler = (msg, instance, trace) => {
  if (config.app.isDev) {
    console.warn('⚠️ Vue警告:', msg)
    console.warn('组件追踪:', trace)
  }
}

// =======================
// 开发环境调试工具
// =======================
if (config.app.isDev) {
  // 暴露调试工具到全局
  (window as any).__APP_DEBUG__ = {
    app,
    router,
    pinia,
    config,
    version: config.app.version
  }

  console.log('🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问')
  console.log(`📱 应用版本: ${config.app.version}`)
  console.log(`🌐 API地址: ${config.api.baseURL}`)
  console.log(`🔌 WebSocket地址: ${config.api.websocket.url}`)
}

// =======================
// PWA支持 (简化版)
// =======================
if (config.app.enablePWA && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ Service Worker 注册成功:', registration.scope)
      })
      .catch((error) => {
        console.error('❌ Service Worker 注册失败:', error)
      })
  })
}

// =======================
// 应用启动
// =======================
app.mount('#app')

console.log(`🚀 ${config.app.name} v${config.app.version} 启动成功!`)
console.log(`🌍 运行环境: ${config.app.isDev ? '开发' : '生产'}`)
console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)

export default app
