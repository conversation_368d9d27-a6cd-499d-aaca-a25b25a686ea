/**
 * 简化的应用主入口文件 - 调试版本
 * 逐步加载依赖，便于定位问题
 */

import { createApp } from 'vue'

console.log('🔍 开始加载应用...')

// 创建一个简单的测试组件
const TestApp = {
  template: `
    <div style="padding: 20px; text-align: center;">
      <h1>🎉 Vue 应用加载成功！</h1>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime">更新时间</button>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString('zh-CN')
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
    }
  }
}

// 创建Vue应用实例
const app = createApp(TestApp)

console.log('✅ Vue 应用实例创建成功')

// 简单的错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('❌ 应用错误:', err)
  console.error('错误信息:', info)
}

// =======================
// 应用启动
// =======================
try {
  app.mount('#app')
  console.log('🚀 应用挂载成功!')
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
}

export default app
