#!/usr/bin/env python3
"""
快速登录修复脚本
专门解决 admin/admin123 登录问题
"""

import sqlite3
import bcrypt
import os
import subprocess
import time
import requests

def hash_password(password: str) -> str:
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def fix_admin_password():
    """修复admin用户密码为admin123"""
    print("🔧 修复admin用户密码...")
    
    db_path = "backend/quant_platform.db"
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建用户表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            hashed_password VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            is_superuser BOOLEAN DEFAULT 0,
            is_verified BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 设置正确的密码
    admin_password = "admin123"
    hashed_password = hash_password(admin_password)
    
    # 检查admin用户是否存在
    cursor.execute('SELECT id FROM users WHERE username = ?', ('admin',))
    admin_exists = cursor.fetchone()
    
    if admin_exists:
        # 更新现有用户
        cursor.execute('''
            UPDATE users 
            SET hashed_password = ?, email = ?, full_name = ?, 
                is_active = 1, is_superuser = 1, is_verified = 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE username = ?
        ''', (hashed_password, '<EMAIL>', '系统管理员', 'admin'))
        print("✅ 更新admin用户密码为: admin123")
    else:
        # 创建新用户
        cursor.execute('''
            INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
            VALUES (?, ?, ?, ?, 1, 1, 1)
        ''', ('admin', '<EMAIL>', '系统管理员', hashed_password))
        print("✅ 创建admin用户，密码为: admin123")
    
    conn.commit()
    conn.close()
    return True

def kill_python_processes():
    """杀掉可能占用端口的Python进程"""
    print("🔄 清理Python进程...")
    try:
        subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                      capture_output=True, text=True)
        time.sleep(2)
        print("✅ Python进程已清理")
    except:
        print("⚠️ 无法清理进程，继续...")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 尝试不同的端口
    ports = [8000, 8001, 8002]
    
    for port in ports:
        try:
            print(f"尝试启动在端口 {port}...")
            
            # 启动后端
            process = subprocess.Popen([
                'python', '-m', 'uvicorn', 'app.main:app', 
                '--host', '0.0.0.0', '--port', str(port)
            ], cwd='backend', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待启动
            time.sleep(5)
            
            # 测试连接
            try:
                response = requests.get(f'http://localhost:{port}/health', timeout=3)
                if response.status_code == 200:
                    print(f"✅ 后端成功启动在端口 {port}")
                    return port, process
            except:
                pass
            
            # 如果失败，杀掉进程
            process.terminate()
            
        except Exception as e:
            print(f"❌ 端口 {port} 启动失败: {e}")
    
    print("❌ 所有端口都启动失败")
    return None, None

def test_login(port):
    """测试登录功能"""
    print(f"\n🧪 测试登录功能 (端口 {port})...")
    
    test_credentials = [
        {'username': 'admin', 'password': 'admin123'},
        {'username': 'demo', 'password': 'demo123'}
    ]
    
    for creds in test_credentials:
        try:
            response = requests.post(
                f'http://localhost:{port}/api/v1/auth/login',
                json=creds,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {creds['username']}/{creds['password']} - 登录成功")
                token = data.get('access_token', '')
                print(f"   Token: {token[:30]}...")
                return True
            else:
                print(f"❌ {creds['username']}/{creds['password']} - 登录失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ {creds['username']}/{creds['password']} - 连接失败: {e}")
    
    return False

def update_frontend_config(port):
    """更新前端配置"""
    if port != 8000:
        print(f"⚙️ 更新前端配置为端口 {port}...")
        
        config_file = "frontend/src/config/index.ts"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换端口
            content = content.replace('localhost:8000', f'localhost:{port}')
            content = content.replace('127.0.0.1:8000', f'127.0.0.1:{port}')
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 前端配置已更新为端口 {port}")

def create_simple_test():
    """创建简单的登录测试"""
    print("📄 创建登录测试页面...")
    
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>登录测试</title>
    <style>
        body { font-family: Arial; max-width: 500px; margin: 50px auto; padding: 20px; }
        input { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; }
        button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h2>量化投资平台 - 登录测试</h2>
    <p><strong>演示账户:</strong> admin / admin123</p>
    
    <input type="text" id="username" placeholder="用户名" value="admin">
    <input type="password" id="password" placeholder="密码" value="admin123">
    <button onclick="testLogin()">测试登录</button>
    
    <div id="result"></div>
    
    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            // 尝试不同端口
            const ports = [8000, 8001, 8002];
            
            for (const port of ports) {
                try {
                    const response = await fetch(`http://localhost:${port}/api/v1/auth/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username, password })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h4>登录成功! (端口 ${port})</h4>
                                <p><strong>用户:</strong> ${data.user.username}</p>
                                <p><strong>Token:</strong> ${data.access_token.substring(0, 30)}...</p>
                            </div>
                        `;
                        return;
                    }
                } catch (error) {
                    console.log(`端口 ${port} 连接失败:`, error);
                }
            }
            
            resultDiv.innerHTML = `
                <div class="result error">
                    <h4>登录失败</h4>
                    <p>无法连接到后端服务，请确保后端正在运行</p>
                </div>
            `;
        }
    </script>
</body>
</html>'''
    
    with open('quick_login_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 创建了 quick_login_test.html")

def main():
    """主函数"""
    print("🔧 快速登录修复")
    print("=" * 40)
    
    # 1. 修复数据库密码
    fix_admin_password()
    
    # 2. 清理进程
    kill_python_processes()
    
    # 3. 启动后端
    port, process = start_backend()
    
    if port:
        # 4. 更新前端配置
        update_frontend_config(port)
        
        # 5. 测试登录
        login_success = test_login(port)
        
        # 6. 创建测试页面
        create_simple_test()
        
        print("\n" + "=" * 40)
        print("✅ 修复完成!")
        print(f"\n📱 演示账户: admin / admin123")
        print(f"🌐 后端地址: http://localhost:{port}")
        print(f"🧪 测试页面: quick_login_test.html")
        print(f"\n🚀 前端启动命令:")
        print(f"   cd frontend && pnpm dev")
        print(f"\n📋 登录地址:")
        print(f"   http://localhost:5173/login")
        
        if login_success:
            print("\n✅ 登录测试成功，可以正常使用!")
        else:
            print("\n⚠️ 登录测试失败，请检查后端日志")
        
        # 保持后端运行
        print(f"\n⏳ 后端服务正在运行，按 Ctrl+C 停止...")
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 停止后端服务...")
            process.terminate()
    else:
        print("\n❌ 后端启动失败，请手动检查问题")

if __name__ == "__main__":
    main()
