# 量化投资平台 - .gitignore 配置文件
# 用于忽略不应该提交到版本控制系统的文件和目录

# =============================================================================
# 操作系统生成的文件
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Python 相关
# =============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so

# Python 虚拟环境 (重要：项目特定的虚拟环境不应入库)
venv/
env/
ENV/
.venv/
.env/
.ENV/
new_venv/
# 项目特定的虚拟环境目录
backend/venv/
backend/venv310/
backend/env/
pip-log.txt
pip-delete-this-directory.txt

# 分发和打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Node.js 相关
# =============================================================================

# 依赖目录
node_modules/
jspm_packages/

# Yarn 相关
yarn.lock
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# npm 相关
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npmrc

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# 包管理器锁文件冲突处理 (项目统一使用pnpm)
# 保留 pnpm-lock.yaml，忽略其他包管理器的锁文件
package-lock.json
yarn.lock

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# 前端构建输出
# =============================================================================

# Vue.js
dist/
.output/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/

# 项目特定的前端构建产物
frontend/dist/
frontend/node_modules/

# React
build/

# Angular
dist/
tmp/
out-tsc/

# Vite
dist-ssr/
*.local

# Rollup
dist/

# Webpack
dist/
build/

# =============================================================================
# 数据库文件
# =============================================================================

# SQLite
*.sqlite
*.sqlite3
*.db
*.db3
*.s3db
*.sl3

# 数据库备份
*.sql
*.dump
*.backup

# =============================================================================
# 日志文件
# =============================================================================

# 应用日志
logs/
*.log
log/
*.log.*

# 系统日志
*.out
*.err

# npm/yarn 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# 缓存目录
# =============================================================================

# 应用缓存
cache/
.cache/
*.cache

# 临时文件
tmp/
temp/
.tmp/
.temp/

# =============================================================================
# 数据和媒体文件
# =============================================================================

# 数据目录
data/
uploads/
storage/
backups/

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.webp
*.svg
*.ico
*.mp4
*.avi
*.mov
*.wmv
*.mp3
*.wav
*.ogg

# 文档文件
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx

# 压缩文件
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z

# =============================================================================
# IDE 和编辑器
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~
.vimrc.local

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath
.factorypath
.buildpath
.target

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# =============================================================================
# 环境配置文件
# =============================================================================

# 环境变量文件
.env
.env.local
.env.development
.env.production
.env.test
.env.*.local

# 配置文件（包含敏感信息）
config.local.js
config.local.json
config.local.yml
config.local.yaml

# =============================================================================
# Docker 相关
# =============================================================================

# Docker Compose 覆盖文件
docker-compose.override.yml
docker-compose.local.yml

# Docker 临时文件
.dockerignore.bak

# =============================================================================
# 监控和性能
# =============================================================================

# Prometheus 数据
prometheus_data/

# Grafana 数据
grafana_data/

# 性能测试结果
performance/
benchmark/

# =============================================================================
# 测试相关
# =============================================================================

# 测试结果
test-results/
test_results/
test-reports/
test_reports/

# 测试覆盖率
coverage/
.nyc_output/

# 截图和视频
screenshots/
videos/
test-screenshots/
playwright-report/
test-output/

# E2E 测试
e2e-results/

# =============================================================================
# 构建和部署
# =============================================================================

# 构建输出
build/
out/
target/

# 部署相关
deploy/
.deploy/

# CI/CD 缓存
.github/workflows/cache/

# =============================================================================
# 安全相关
# =============================================================================

# 密钥文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.csr

# 证书文件
ssl/
certs/
certificates/

# 私有配置
private/
secrets/

# =============================================================================
# 临时和测试文件
# =============================================================================

# 临时文件
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# 测试文件
test_*
*_test
*.test

# 调试文件
debug*
*.debug

# 示例和演示文件
example*
demo*
sample*

# 原型文件
prototype*
poc*

# =============================================================================
# 量化交易平台特定
# =============================================================================

# 市场数据缓存
market_data/
realtime_data/
historical_data/
tick_data/

# 策略回测结果
backtest_results/
strategy_results/

# 交易记录
trades/
orders/
positions/

# 风控数据
risk_data/

# 报告文件
reports/
analysis/

# 配置备份
config_backup/

# 数据库快照
snapshots/

# 索引文件
index/
indices/

# 临时分析文件
analysis_*
report_*

# MCP 相关
mcp_*/
browser-tools-mcp/
mcp-use/
mcp/node_modules/
mcp/puppeteer/screenshots*/
mcp/puppeteer/*_test_*.png
mcp/puppeteer/*_test_*.json
mcp/puppeteer/debug_*.png

# Puppeteer 相关测试文件
*_test_*.png
*_test_*.jpg
*_test_*.json
*_report_*.json
*_report_*.md
*_report_*.html

# 清理和归档
cleanup_*
archive/
redundant/

# 深度测试文件
deep_*
comprehensive_*
final_*

# 状态和验证文件
status_*
verification_*

# =============================================================================
# 特殊的忽略模式
# =============================================================================

# 时间戳命名的文件
*_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]*
*_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]_*
*_[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]*

# 测试和调试相关的根目录文件
/*.test.*
/debug_*
/temp_*
/backup_*
/final_*
/comprehensive_*
/smart_*
/user_experience_*
/improvement_*
/real_user_*
/puppeteer_*
/server_diagnostic*
/simple_*
/minimal_*
/verify_*
/test_*

# 具体的大文件和目录
/完整深度测试总结报告.md
/实盘交易系统升级说明.md

# =============================================================================
# 保留重要文件 - 明确不忽略
# =============================================================================

# 保留示例配置文件
!.env.example
!config.example.*
!docker-compose.example.yml

# 保留空目录的占位文件
!.gitkeep
!.keep

# 保留重要的构建脚本
!scripts/

# 保留文档目录中的重要静态资源（谨慎选择）
!docs/**/README.md
!docs/**/*.md

# 保留项目核心配置
!/README.md
!/LICENSE
!/package.json
!/requirements.txt
!/docker-compose.yml
!/docker-compose.prod.yml
!/.github/
!/docs/DEPLOYMENT_AND_MONITORING_GUIDE.md
!/config/
!/scripts/platform-start.py
!/scripts/platform-config.json
!/scripts/deploy-linux.sh
!/start.sh
# 开发测试文件
test_auth_server.py
*_test.py
*_test.js
test_*.py
test_*.js
debug_*.py
debug_*.js

# 测试页面
test-*.html
debug-*.html
quick-test.html

# 测试报告
*_report_*.json
*_report_*.md
*_report_*.html
health-check-report.json
maintenance-report.json

# 监控数据
monitoring-reports/

# 临时数据库
app.db
quant_dev.db
quant_test.db

# =============================================================================
# 项目特定的大文件和目录（2025-01-07 新增）
# =============================================================================

# 后端虚拟环境（不应入库）
backend/venv/
backend/.venv/
backend/env/

# 大数据文件目录
data/historical/
data/cache/
data/processed/
data/analysis/
data/realtime/

# 历史CSV数据文件
*.csv
data/**/*.csv

# 数据库文件
*.db
data/**/*.db
quantplatform.db

# 测试报告和截图（仓库瘦身）
*_report.json
*_report.md
*_report.html
screenshots/
test-screenshots/
capture_*.png
capture_*.jpg

# 清理脚本生成的文件
cleanup_*.py
simple_cleanup.py
advanced_cleanup.py

# 浏览器测试相关
deep_puppeteer_test.js
capture_screenshots.py
detailed_*.py
detailed_*.js

# 项目完成报告（避免重复）
PROJECT_COMPLETION_SUMMARY.md
README_FIXED.md

# 根目录测试文件
/test_*.py
/test_*.html
/test_*.js

# ===== 2025年仓库清理新增规则 =====
# 前端构建产物和依赖
**/node_modules/
**/package-lock.json
**/pnpm-lock.yaml
**/.pnpm/
**/dist/
**/.vite/
**/.turbo/

# 后端Python缓存和虚拟环境
**/__pycache__/
*.pyc
*.pyo
*.pyd
.venv/
venv/
env/
.env

# 数据库和敏感数据
*.db
*.sqlite
*.sqlite3
data/**/*.db
backend/data/
archive/temp/
test.db

# 日志和临时文件
*.log
logs/
**/*.log
*.tmp
*.swp
.DS_Store
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo

# 构建和缓存目录
cache/
.cache/
build/
coverage/
.nyc_output/

# 时间戳文件和测试报告
*_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]*
*_report_*.json
*_report_*.md
comprehensive_*.py
final_*.py
debug_*.png
test_*.png

# MCP和Puppeteer测试文件
mcp/**/node_modules/
mcp/puppeteer/screenshots*/
*_test_*.png
*_test_*.json

# 大文件和归档
*.tar.gz
*.zip
*.rar
archive/
backup_*/

# =============================================================================
# 数据模型结构规范 (2025-01-12 新增)
# =============================================================================

# 模型备份文件
*.backup
model_backups/

# 禁止在遗留模型目录添加新的模型文件
backend/app/models/*.py
!backend/app/models/__init__.py

# 确保只有统一的模型目录生效
# 所有模型应在 backend/app/db/models/ 目录下