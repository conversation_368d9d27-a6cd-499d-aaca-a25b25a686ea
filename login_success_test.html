<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录成功测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        .demo-accounts {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            word-break: break-all;
        }
        .success {
            background: rgba(76, 175, 80, 0.8);
            border: 1px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.8);
            border: 1px solid #f44336;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
        }
        .quick-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-btn {
            flex: 1;
            padding: 8px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 量化投资平台 - 登录测试</h1>
        
        <div class="status" id="backendStatus">
            🔄 检查后端状态...
        </div>
        
        <div class="demo-accounts">
            <h3>📱 演示账户</h3>
            <p><strong>管理员:</strong> admin / admin123</p>
            <p><strong>演示用户:</strong> demo / demo123</p>
        </div>
        
        <div class="quick-buttons">
            <button class="quick-btn" onclick="fillAdmin()">管理员登录</button>
            <button class="quick-btn" onclick="fillDemo()">演示用户登录</button>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit">🔐 测试登录</button>
        </form>
        
        <div id="result"></div>
    </div>
    
    <script>
        // 检查后端状态
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('backendStatus').innerHTML = '✅ 后端服务正常运行';
                    document.getElementById('backendStatus').style.color = '#4CAF50';
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                document.getElementById('backendStatus').innerHTML = '❌ 后端服务未运行';
                document.getElementById('backendStatus').style.color = '#f44336';
            }
        }
        
        // 填充管理员账户
        function fillAdmin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
        }
        
        // 填充演示账户
        function fillDemo() {
            document.getElementById('username').value = 'demo';
            document.getElementById('password').value = 'demo123';
        }
        
        // 登录测试
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            // 显示加载状态
            resultDiv.innerHTML = `
                <div class="result" style="background: rgba(255, 193, 7, 0.8);">
                    <h4>🔄 正在登录...</h4>
                    <p>请稍候...</p>
                </div>
            `;
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ 登录成功!</h4>
                            <p><strong>用户:</strong> ${data.user.username} (${data.user.full_name})</p>
                            <p><strong>邮箱:</strong> ${data.user.email}</p>
                            <p><strong>角色:</strong> ${data.user.role}</p>
                            <p><strong>权限:</strong> ${data.user.permissions.join(', ')}</p>
                            <p><strong>Token:</strong> ${data.access_token.substring(0, 50)}...</p>
                            <p><strong>有效期:</strong> ${data.expires_in / 3600} 小时</p>
                            <hr style="margin: 15px 0; border: 1px solid rgba(255,255,255,0.3);">
                            <p style="color: #e8f5e8;">🎉 现在可以在前端页面使用此账户登录了！</p>
                            <p style="font-size: 14px;">前端地址: <a href="http://localhost:5173/login" target="_blank" style="color: #90caf9;">http://localhost:5173/login</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ 登录失败</h4>
                            <p><strong>错误:</strong> ${data.detail || '未知错误'}</p>
                            <p><strong>状态码:</strong> ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>🚫 连接失败</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p>请确保后端服务正在运行在 http://localhost:8000</p>
                        <p>启动命令: <code>python simple_backend.py</code></p>
                    </div>
                `;
            }
        });
        
        // 页面加载时检查后端状态
        window.onload = function() {
            checkBackendStatus();
            // 每30秒检查一次后端状态
            setInterval(checkBackendStatus, 30000);
        };
    </script>
</body>
</html>
