<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed, ref, onMounted } from 'vue'
import PageLoading from '@/components/common/PageLoading.vue'
import { globalLoading } from '@/composables/core/usePageLoading'

// 延迟初始化UI store
const isDarkMode = ref(false)

onMounted(async () => {
  try {
    // 动态导入store，确保Pinia已经初始化
    const { useUIStore } = await import('@/stores/modules/ui')
    const uiStore = useUIStore()
    isDarkMode.value = uiStore.isDarkMode

    // 监听暗色模式变化
    uiStore.$subscribe((mutation, state) => {
      isDarkMode.value = state.isDarkMode
    })
  } catch (error) {
    console.warn('UI Store初始化失败:', error)
  }
})

// 全局加载状态
const { isLoading, loadingText, loadingTips } = globalLoading
</script>

<template>
  <div id="app-wrapper" :class="{ 'dark': isDarkMode }">
    <RouterView />
    <!-- 全局加载指示器 -->
    <PageLoading
      :show="isLoading"
      :text="loadingText"
      :tips="loadingTips"
    />
  </div>
</template>

<style lang="scss">
#app-wrapper {
  width: 100%;
  min-height: 100vh;
  height: auto !important;
  overflow: visible;
}

/* 全局布局修复 */
:deep(.default-layout) {
  height: auto !important;
  min-height: 100vh !important;
}

:deep(.layout-content) {
  height: auto !important;
  overflow: visible !important;
}

:deep(.market-content) {
  height: auto !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}
</style>
