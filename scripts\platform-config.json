{"environments": {"development": {"backend_port": 8000, "frontend_port": 5173, "database_url": "sqlite:///./data/quant.db", "redis_url": "redis://localhost:6379/0", "log_level": "DEBUG"}, "production": {"backend_port": 8000, "frontend_port": 80, "database_url": "****************************************************/quant_db", "redis_url": "redis://redis:6379/0", "log_level": "INFO"}, "testing": {"backend_port": 8000, "frontend_port": 5174, "database_url": "sqlite:///./data/quant_test.db", "redis_url": "redis://localhost:6379/1", "log_level": "DEBUG"}}, "services": {"backend": {"path": "backend", "start_cmd": {"windows": "python app/main.py", "linux": "python3 app/main.py", "darwin": "python3 app/main.py", "docker": "python app/main.py"}, "health_check": "/health", "dependencies": {"files": ["requirements.txt", "app/main.py"], "commands": ["python", "pip"]}}, "frontend": {"path": "frontend", "start_cmd": {"windows": "npm run dev", "linux": "npm run dev", "darwin": "npm run dev", "docker": "npm run serve"}, "health_check": "/", "dependencies": {"files": ["package.json", "package-lock.json"], "commands": ["node", "npm"]}}, "database": {"path": "data", "start_cmd": {"docker": "echo 'Database in container'"}, "health_check": "/db/health"}, "redis": {"path": "cache", "start_cmd": {"docker": "echo 'Redis in container'"}, "health_check": "/cache/health"}}, "docker": {"compose_files": {"development": "docker-compose.yml", "production": "docker-compose.prod.yml", "monitoring": "docker/docker-compose.monitoring.yml"}, "profiles": {"minimal": ["backend", "frontend", "redis"], "full": ["backend", "frontend", "redis", "postgres", "nginx"], "monitoring": ["backend", "frontend", "redis", "postgres", "prometheus", "grafana"]}}, "monitoring": {"metrics_port": 9090, "grafana_port": 3001, "health_check_interval": 30, "log_retention_days": 30}, "security": {"cors_origins": ["http://localhost:5173", "http://localhost:3000"], "allowed_hosts": ["localhost", "127.0.0.1"], "ssl_enabled": false}}