"""
独立的简化后端，不依赖复杂模块
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import json

# 创建应用
app = FastAPI(
    title="量化投资平台 API",
    description="简化版量化投资平台后端API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
mock_stocks = [
    {"symbol": "000001", "name": "平安银行", "price": 12.50, "change": 0.05, "change_pct": 0.40},
    {"symbol": "000002", "name": "万科A", "price": 18.30, "change": -0.12, "change_pct": -0.65},
    {"symbol": "600000", "name": "浦发银行", "price": 8.90, "change": 0.08, "change_pct": 0.91},
    {"symbol": "600036", "name": "招商银行", "price": 35.20, "change": 0.15, "change_pct": 0.43},
    {"symbol": "000858", "name": "五粮液", "price": 128.50, "change": -2.30, "change_pct": -1.76}
]

# 基础路由
@app.get("/")
async def root():
    return {
        "message": "量化投资平台 API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "database": "connected"
    }

# API v1 路由
@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "api_version": "v1", 
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/stocks")
async def get_stocks():
    return {
        "code": 200,
        "message": "success",
        "data": mock_stocks,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/realtime/{symbol}")
async def get_realtime_data(symbol: str):
    # 查找股票
    stock = next((s for s in mock_stocks if s["symbol"] == symbol), None)
    if not stock:
        return {"code": 404, "message": "股票不存在", "data": None}
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "symbol": symbol,
            "price": stock["price"],
            "change": stock["change"],
            "change_pct": stock["change_pct"],
            "volume": 1000000,
            "turnover": stock["price"] * 1000000,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.post("/api/v1/auth/login")
async def login(user_data: dict):
    # 简单的登录验证
    return {
        "code": 200,
        "message": "登录成功",
        "data": {
            "token": "demo_token_123456",
            "user": {
                "id": 1,
                "username": "demo",
                "name": "演示用户"
            }
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/user/info")
async def get_user_info():
    return {
        "code": 200,
        "message": "success",
        "data": {
            "id": 1,
            "username": "demo",
            "name": "演示用户",
            "email": "<EMAIL>"
        }
    }

if __name__ == "__main__":
    print("Starting Quant Platform Backend...")
    print("API Docs: http://localhost:8002/docs")
    print("Health Check: http://localhost:8002/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        reload=False
    )